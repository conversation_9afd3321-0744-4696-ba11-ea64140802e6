using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Interfaces.User;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
	public class AppUserRepository : Repository<AppUser>, IAppUserRepository
	{
		public AppUserRepository(ApplicationDbContext context) : base(context)
		{
		}

		public AppUser Authenticate(string userName, string password)
		{
			throw new NotImplementedException();
		}

		public async Task<bool> IsEmailExistsAsync(string email)
		{
			return await _context.Users.AnyAsync(x => x.Email.ToLower() == email.ToLower() && !x.IsDeleted);
		}

		public async Task<bool> IsEmailExistsAsync(string email, int excludeUserId)
		{
			return await _context.Users.AnyAsync(x => x.Email.ToLower() == email.ToLower() && x.Id != excludeUserId && !x.IsDeleted);
		}

		public async Task<AppUser?> GetByEmailAsync(string email)
		{
			return await _context.Users.FirstOrDefaultAsync(x => x.Email.ToLower() == email.ToLower() && !x.IsDeleted);
		}

		public async Task<IEnumerable<AppUser>> GetAllActiveAsync()
		{
			return await _context.Users.Where(x => x.IsActive && !x.IsDeleted).ToListAsync();
		}

		public async Task<bool> SetHasCompletedInitialSetupAsync(int userId)
		{
			var user = await _context.Users.FindAsync(userId);
			if (user == null) return false;

			user.HasCompletedInitialSetup = true;
			await _context.SaveChangesAsync();
			return true;
		}
	}
}
/*
Nếu bạn chỉ muốn cập nhật HasCompletedInitialSetup khi tạo dự án lần đầu, bạn cần xử lý ở các file sau:

1. **AppUserRepository.cs** (file hiện tại):
   - Đảm bảo có phương thức SetHasCompletedInitialSetupAsync như đã có.
   - Gọi phương thức này sau khi tạo user mới (khi tạo dự án lần đầu).

2. **Service hoặc UseCase tạo dự án/user** (ví dụ: UserService, ProjectService, hoặc nơi xử lý logic tạo user/dự án):
   - Sau khi tạo user thành công, gọi `SetHasCompletedInitialSetupAsync(userId)` để cập nhật cờ này.

3. **Controller hoặc API Endpoint tạo user/dự án**:
   - Đảm bảo sau khi tạo user, service được gọi để cập nhật HasCompletedInitialSetup.

4. **Migration (nếu cần)**:
   - Nếu trường HasCompletedInitialSetup chưa có trong bảng Users, cần tạo migration để thêm trường này.

**Tóm lại:**  
- Repository: Đã có phương thức cập nhật.  
- Service/UseCase: Gọi phương thức cập nhật sau khi tạo user/dự án lần đầu.  
- Controller/API: Đảm bảo luồng tạo user/dự án có gọi service cập nhật.  
- Migration: Đảm bảo trường HasCompletedInitialSetup tồn tại.

Bạn cần kiểm tra và cập nhật các file/service liên quan đến luồng tạo user/dự án lần đầu để gọi hàm cập nhật này.
*/
