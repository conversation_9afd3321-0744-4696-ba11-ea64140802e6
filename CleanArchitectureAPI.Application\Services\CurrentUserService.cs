using System;
using System.Security.Claims;
using CleanArchitectureAPI.Application.Interfaces;
using Microsoft.AspNetCore.Http;

namespace CleanArchitectureAPI.Application.Services
{
    public class CurrentUserService : ICurrentUserService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CurrentUserService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public int GetUserId()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null)
                throw new UnauthorizedAccessException("Không có HttpContext.");

            var claim = httpContext.User?.FindFirst(ClaimTypes.NameIdentifier);

            if (claim == null || string.IsNullOrWhiteSpace(claim.Value) || !int.TryParse(claim.Value, out int userId))
                throw new UnauthorizedAccessException("Không tìm thấy ID người dùng trong token.");

            return userId;
        }

        public string GetUserName()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null)
                throw new UnauthorizedAccessException("Không có HttpContext.");

            var claim = httpContext.User?.FindFirst(ClaimTypes.Email);

            if (claim == null || string.IsNullOrWhiteSpace(claim.Value))
                throw new UnauthorizedAccessException("Không tìm thấy tên người dùng trong token.");

            return claim.Value;
        }
    }
}
