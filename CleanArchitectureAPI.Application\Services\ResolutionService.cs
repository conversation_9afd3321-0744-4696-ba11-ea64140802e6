using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.API.Requests.Resolution;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.Requests.ResolutionRequests;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using Mapster;

namespace CleanArchitectureAPI.Application.Services
{
    public class ResolutionService : IResolutionService
    {
        private readonly IResolutionRepository _resolutionRepository;
        private readonly IProjectAccessService _projectAccessService;
        private readonly ICurrentUserService _currentUserService;

        public ResolutionService(IResolutionRepository resolutionRepository, IProjectAccessService projectAccessService, ICurrentUserService currentUserService)
        {
            _resolutionRepository = resolutionRepository;
            _projectAccessService = projectAccessService;
            _currentUserService = currentUserService;
        }

        public async Task<Result<ResolutionDto>> CreateAsync(CreateResolutionRequest request)
        {
            int? projectId = await _projectAccessService.ResolveProjectIdAsync(request.ProjectKey);
            if (projectId is null)
                return Result<ResolutionDto>.Failure(ErrorCode.NotFound, "Project không tồn tại");

            // Check if name is duplicated
            var isNameDuplicated = await _resolutionRepository.IsNameDuplicated(request.ProjectKey, request.Name);
            if (isNameDuplicated)
            {
                return Result<ResolutionDto>.Failure(ErrorCode.Conflict, "Tên giải pháp đã tồn tại trong dự án này.");
            }

            var resolution = new Resolution
            {
                Name = request.Name,
                Description = request.Description,
                ProjectKey = request.ProjectKey,
                Order = await _resolutionRepository.GetMaxOrderInProjectAsync(request.ProjectKey) + 1,
                ProjectId = projectId.Value,
                CreatedById = _currentUserService.GetUserId(),
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
            };

            await _resolutionRepository.CreateAsync(resolution);

            return Result<ResolutionDto>.Success(resolution.Adapt<ResolutionDto>());
        }

        public async Task<Result<IEnumerable<ResolutionDto>>> GetAllByProjectKeyAsync(string projectKey)
        {
            IEnumerable<Resolution> resolutions = await _resolutionRepository.GetAllByProjectKeyAsync(projectKey);
            var resolutionDtos = resolutions.Adapt<IEnumerable<ResolutionDto>>();
            return Result<IEnumerable<ResolutionDto>>.Success(resolutionDtos);
        }

        public async Task<Result<ResolutionDto>> GetByIdAsync(int id)
        {
            var resolution = await _resolutionRepository.GetByIdAsync(id);
            if (resolution == null)
                return Result<ResolutionDto>.Failure(ErrorCode.NotFound, "Không tìm thấy giải pháp");

            return Result<ResolutionDto>.Success(resolution.Adapt<ResolutionDto>());
        }

        public async Task<Result<bool>> ReorderAsync(ReorderResolutionRequest request)
        {
            if (string.IsNullOrWhiteSpace(request.ProjectKey) || request.ResolutionIdsInOrder == null || !request.ResolutionIdsInOrder.Any())
            {
                return Result<bool>.Failure("Dữ liệu không hợp lệ.");
            }

            await _resolutionRepository.ReorderResolutionsAsync(request.ProjectKey, request.ResolutionIdsInOrder);
            return Result<bool>.Success(true);
        }

        public async Task<Result<bool>> SoftDeleteAsync(int id)
        {
            var resolution = await _resolutionRepository.GetByIdAsync(id);
            if (resolution == null)
            {
                return Result<bool>.Failure("Không tìm thấy giải pháp.");
            }
            resolution.DeletedById = _currentUserService.GetUserId();
            resolution.DeletedAt = DateTime.UtcNow;
            await _resolutionRepository.SoftDeleteAsync(id);

            return Result<bool>.Success(true);
        }

        public async Task<Result<bool>> UpdateAsync(int id, UpdateResolutionRequest request)
        {
            var resolution = await _resolutionRepository.GetByIdAsync(id);
            if (resolution == null)
            {
                return Result<bool>.Failure("Không tìm thấy giải pháp.");
            }

            var existingResolutions = await _resolutionRepository.GetAllByProjectKeyAsync(request.ProjectKey);
            var isNameDuplicated = existingResolutions
                .Where(s => s.Id != id && s.ProjectKey == resolution.ProjectKey && s.Name == request.Name)
                .Any();

            if (isNameDuplicated)
            {
                return Result<bool>.Failure("Tên giải pháp đã tồn tại trong dự án này.");
            }
            resolution.Name = request.Name;
            resolution.Description = request.Description;
            resolution.LastModifiedAt = DateTime.UtcNow;
            resolution.LastModifiedById = _currentUserService.GetUserId();
            resolution.IsActive = request.IsActive;

            await _resolutionRepository.UpdateAsync(resolution);

            return Result<bool>.Success(true);
        }
    }
}
