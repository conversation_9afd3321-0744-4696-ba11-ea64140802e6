using FluentValidation;
using CleanArchitectureAPI.Application.Requests.AppUser;

namespace CleanArchitectureAPI.Application.Validators.AppUser
{
    public class ChangePasswordRequestValidator : AbstractValidator<ChangePasswordRequest>
    {
        public ChangePasswordRequestValidator()
        {
            RuleFor(x => x.CurrentPassword)
                .NotEmpty().WithMessage("Vui lòng nhập mật khẩu hiện tại.");

            RuleFor(x => x.NewPassword)
                .NotEmpty().WithMessage("Vui lòng nhập mật khẩu mới.")
                .MinimumLength(6).WithMessage("Mật khẩu mới phải có ít nhất 6 ký tự.")
                .MaximumLength(100).WithMessage("Mật khẩu mới không được vượt quá 100 ký tự.");

            RuleFor(x => x.ConfirmPassword)
                .NotEmpty().WithMessage("<PERSON><PERSON> lòng xác nhận mật khẩu mới.")
                .Equal(x => x.NewPassword).WithMessage("Xác nhận mật khẩu không khớp.");
        }
    }
}
