### AppUser API Test Examples
### Base URL: https://localhost:7000

### 1. Register a new user
POST https://localhost:7000/api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### 2. Login with the user
POST https://localhost:7000/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### 3. Get current user profile (requires login)
GET https://localhost:7000/api/appuser/profile
Authorization: Bearer {{token}}

### 4. Create a new user (admin function)
POST https://localhost:7000/api/appuser
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+84123456789",
  "bio": "Software Developer",
  "address": "123 Main St, Ho Chi Minh City",
  "status": 0
}

### 5. Get all users
GET https://localhost:7000/api/appuser
Authorization: Bearer {{token}}

### 6. Get user by ID
GET https://localhost:7000/api/appuser/1
Authorization: Bearer {{token}}

### 7. Update user profile
PUT https://localhost:7000/api/appuser/profile
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "firstName": "John Updated",
  "lastName": "Doe Updated",
  "phone": "+***********",
  "bio": "Senior Software Developer",
  "address": "456 New Street, Ho Chi Minh City"
}

### 8. Update specific user (admin function)
PUT https://localhost:7000/api/appuser/2
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "firstName": "Jane",
  "lastName": "Smith",
  "phone": "+***********",
  "bio": "Project Manager",
  "address": "789 Business St, Ho Chi Minh City",
  "status": 0
}

### 9. Change password
PUT https://localhost:7000/api/appuser/change-password
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "currentPassword": "password123",
  "newPassword": "newpassword456",
  "confirmPassword": "newpassword456"
}

### 10. Delete user (soft delete)
DELETE https://localhost:7000/api/appuser/2
Authorization: Bearer {{token}}

### 11. Logout
POST https://localhost:7000/api/auth/logout
Authorization: Bearer {{token}}

### Test Cases for Validation Errors

### 12. Register with invalid email
POST https://localhost:7000/api/auth/register
Content-Type: application/json

{
  "email": "invalid-email",
  "password": "password123"
}

### 13. Register with short password
POST https://localhost:7000/api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "123"
}

### 14. Create user with missing required fields
POST https://localhost:7000/api/appuser
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### 15. Change password with wrong current password
PUT https://localhost:7000/api/appuser/change-password
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "currentPassword": "wrongpassword",
  "newPassword": "newpassword456",
  "confirmPassword": "newpassword456"
}

### 16. Change password with mismatched confirmation
PUT https://localhost:7000/api/appuser/change-password
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "currentPassword": "password123",
  "newPassword": "newpassword456",
  "confirmPassword": "differentpassword"
}

### 17. Access protected endpoint without token
GET https://localhost:7000/api/appuser/profile

### 18. Register with existing email
POST https://localhost:7000/api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### Notes:
# Replace {{token}} with actual JWT token received from login response
# Update the base URL if your API runs on different port
# Some endpoints require admin privileges
# All timestamps are in UTC format
