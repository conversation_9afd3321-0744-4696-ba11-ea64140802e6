﻿using CleanArchitecture.API.Extension;
using CleanArchitecture.API.Requests.Priority;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Requests.PriorityRequests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PrioritiesController : ControllerBase
    {
        private readonly IPriorityService _priorityService;

        public PrioritiesController(IPriorityService priorityService)
        {
            _priorityService = priorityService;
        }

        [HttpGet("project/{projectKey}")]
        public async Task<IActionResult> GetAllPrioritiesByProjectKey([FromRoute] string projectKey)
        {
            var result = await _priorityService.GetAllByProjectKeyAsync(projectKey);
            return result.ToActionResult();
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetPriorityById(int id)
        {
            var result = await _priorityService.GetByIdAsync(id);
            return result.ToActionResult();
        }

        [HttpPost]
        public async Task<IActionResult> CreatePriority([FromBody] CreatePriorityRequest request)
        {
            var result = await _priorityService.CreateAsync(request);

            if (!result.IsSuccess)
            {
                return result.ToActionResult();
            }
            return result.ToActionResult();
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdatePriority(int id, [FromBody] UpdatePriorityRequest request)
        {
            var result = await _priorityService.UpdateAsync(id, request);
            return result.ToActionResult();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> SoftDeletePriority(int id)
        {
            var result = await _priorityService.SoftDeleteAsync(id);
            return result.ToActionResult();
        }

        [HttpPatch("reorder")]
        public async Task<IActionResult> ReorderPriority([FromBody] ReorderPriorityRequest request)
        {
            var result = await _priorityService.ReorderAsync(request);
            return result.ToActionResult();
        }
    }
}