using CleanArchitectureAPI.Domain.Enums;

namespace CleanArchitectureAPI.Application.Requests.AppUser
{
    public class UpdateAppUserRequest
    {
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Bio { get; set; }
        public string? Address { get; set; }
        public UserStatus Status { get; set; }
    }
}
