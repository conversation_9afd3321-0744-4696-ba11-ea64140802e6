using CleanArchitecture.API.Extension;
using CleanArchitecture.API.Requests.Milestone;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Requests.MilestoneRequests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class MilestonesController : ControllerBase
    {
        private readonly IMilestoneService _milestoneService;

        public MilestonesController(IMilestoneService milestoneService)
        {
            _milestoneService = milestoneService;
        }

        [HttpGet("project/{projectKey}")]
        public async Task<IActionResult> GetAllMilestonesByProjectKey([FromRoute] string projectKey)
        {
            var result = await _milestoneService.GetAllByProjectKeyAsync(projectKey);
            return result.ToActionResult();
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetMilestoneById(int id)
        {
            var result = await _milestoneService.GetByIdAsync(id);
            return result.ToActionResult();
        }

        [HttpPost]
        public async Task<IActionResult> CreateMilestone([FromBody] CreateMilestoneRequest request)
        {
            var result = await _milestoneService.CreateAsync(request);

            if (!result.IsSuccess)
            {
                return result.ToActionResult();
            }
            return result.ToActionResult();
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateMilestone(int id, [FromBody] UpdateMilestoneRequest request)
        {
            var result = await _milestoneService.UpdateAsync(id, request);
            return result.ToActionResult();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> SoftDeleteMilestone(int id)
        {
            var result = await _milestoneService.SoftDeleteAsync(id);
            return result.ToActionResult();
        }

        [HttpPatch("reorder")]
        public async Task<IActionResult> ReorderMilestone([FromBody] ReorderMilestoneRequest request)
        {
            var result = await _milestoneService.ReorderAsync(request);
            return result.ToActionResult();
        }
    }
}