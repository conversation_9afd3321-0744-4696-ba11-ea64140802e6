﻿using CleanArchitecture.API.Extension;
using CleanArchitecture.API.Requests.Resolution;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Requests.ResolutionRequests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ResolutionsController : ControllerBase
    {
        private readonly IResolutionService _resolutionService;

        public ResolutionsController(IResolutionService resolutionService)
        {
            _resolutionService = resolutionService;
        }

        [HttpGet("project/{projectKey}")]
        public async Task<IActionResult> GetAllResolutionsByProjectKey([FromRoute] string projectKey)
        {
            var result = await _resolutionService.GetAllByProjectKeyAsync(projectKey);
            return result.ToActionResult();
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetResolutionById(int id)
        {
            var result = await _resolutionService.GetByIdAsync(id);
            return result.ToActionResult();
        }

        [HttpPost]
        public async Task<IActionResult> CreateResolution([FromBody] CreateResolutionRequest request)
        {
            var result = await _resolutionService.CreateAsync(request);

            if (!result.IsSuccess)
            {
                return result.ToActionResult();
            }
            return result.ToActionResult();
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateResolution(int id, [FromBody] UpdateResolutionRequest request)
        {
            var result = await _resolutionService.UpdateAsync(id, request);
            return result.ToActionResult();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> SoftDeleteResolution(int id)
        {
            var result = await _resolutionService.SoftDeleteAsync(id);
            return result.ToActionResult();
        }

        [HttpPatch("reorder")]
        public async Task<IActionResult> ReorderResolution([FromBody] ReorderResolutionRequest request)
        {
            var result = await _resolutionService.ReorderAsync(request);
            return result.ToActionResult();
        }
    }
}