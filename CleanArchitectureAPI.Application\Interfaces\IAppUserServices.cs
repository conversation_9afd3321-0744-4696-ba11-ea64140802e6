﻿using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Requests.AppUser;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IAppUserServices
    {
        Task<Result<IEnumerable<AppUserDto>>> GetAllAsync();
        Task<Result<AppUserDto>> GetByIdAsync(int id);
        Task<Result<AppUserDto>> CreateAsync(CreateAppUserRequest request);
        Task<Result<AppUserDto>> UpdateAsync(int id, UpdateAppUserRequest request);
        Task<Result<bool>> DeleteAsync(int id);
        Task<Result<AppUserDto>> UpdateProfileAsync(int id, UpdateProfileRequest request);
        Task<Result<bool>> ChangePasswordAsync(int id, ChangePasswordRequest request);
        Task<Result<AppUserDto>> GetCurrentUserAsync();
        Task<Result<bool>> SetHasCompletedInitialSetupAsync(int userId);

	}
}
