﻿using CleanArchitecture.API.Requests.Resolution;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Requests.ResolutionRequests;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IResolutionService
    {
        Task<Result<IEnumerable<ResolutionDto>>> GetAllByProjectKeyAsync(string projectKey);
        Task<Result<ResolutionDto>> GetByIdAsync(int id);
        Task<Result<ResolutionDto>> CreateAsync(CreateResolutionRequest request);
        Task<Result<bool>> UpdateAsync(int id, UpdateResolutionRequest request);
        Task<Result<bool>> SoftDeleteAsync(int id);
        Task<Result<bool>> ReorderAsync(ReorderResolutionRequest request);
    }
}