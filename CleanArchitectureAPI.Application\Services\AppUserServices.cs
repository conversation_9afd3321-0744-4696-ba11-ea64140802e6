using CleanArchitecture.Shared.Helpers;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.Requests.AppUser;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Interfaces.User;
using Mapster;

namespace CleanArchitectureAPI.Application.Services
{
    public class AppUserServices : IAppUserServices
    {
        private readonly IAppUserRepository _appUserRepository;
        private readonly ICurrentUserService _currentUserService;

        public AppUserServices(IAppUserRepository appUserRepository, ICurrentUserService currentUserService)
        {
            _appUserRepository = appUserRepository;
            _currentUserService = currentUserService;
        }

        public async Task<Result<IEnumerable<AppUserDto>>> GetAllAsync()
        {
            var users = await _appUserRepository.GetAllActiveAsync();
            var userDtos = users.Adapt<IEnumerable<AppUserDto>>();
            return Result<IEnumerable<AppUserDto>>.Success(userDtos);
        }

        public async Task<Result<AppUserDto>> GetByIdAsync(int id)
        {
            var user = await _appUserRepository.GetByIdAsync(id);
            if (user == null || user.IsDeleted)
                return Result<AppUserDto>.Failure(ErrorCode.NotFound, "Không tìm thấy người dùng");

            return Result<AppUserDto>.Success(user.Adapt<AppUserDto>());
        }

        public async Task<Result<AppUserDto>> CreateAsync(CreateAppUserRequest request)
        {
            // Check if email already exists
            if (await _appUserRepository.IsEmailExistsAsync(request.Email))
                return Result<AppUserDto>.Failure(ErrorCode.Conflict, "Email đã tồn tại trong hệ thống");

            var user = new AppUser
            {
                FirstName = request.FirstName,
                LastName = request.LastName,
                Email = request.Email.ToLower(),
                PasswordHash = PasswordHelper.HassPassword(request.Password),
                Phone = request.Phone,
                Bio = request.Bio,
                Address = request.Address,
                Status = request.Status,
                CreatedAt = DateTime.UtcNow,
                CreatedById = _currentUserService.GetUserId(),
				HasCompletedInitialSetup = true,
                IsActive = true,
                LastActive = DateTime.UtcNow,
                LastLoginTime = DateTime.UtcNow
            };

            await _appUserRepository.CreateAsync(user);
            return Result<AppUserDto>.Success(user.Adapt<AppUserDto>());
        }

        public async Task<Result<AppUserDto>> UpdateAsync(int id, UpdateAppUserRequest request)
        {
            var user = await _appUserRepository.GetByIdAsync(id);
            if (user == null || user.IsDeleted)
                return Result<AppUserDto>.Failure(ErrorCode.NotFound, "Không tìm thấy người dùng");

            user.FirstName = request.FirstName;
            user.LastName = request.LastName;
            user.Phone = request.Phone;
            user.Bio = request.Bio;
            user.Address = request.Address;
            user.Status = request.Status;
            user.LastModifiedAt = DateTime.UtcNow;
            user.LastModifiedById = _currentUserService.GetUserId();

            await _appUserRepository.UpdateAsync(user);
            return Result<AppUserDto>.Success(user.Adapt<AppUserDto>());
        }

        public async Task<Result<bool>> DeleteAsync(int id)
        {
            var user = await _appUserRepository.GetByIdAsync(id);
            if (user == null || user.IsDeleted)
                return Result<bool>.Failure(ErrorCode.NotFound, "Không tìm thấy người dùng");

            user.IsDeleted = true;
            user.DeletedAt = DateTime.UtcNow;
            user.DeletedById = _currentUserService.GetUserId();

            await _appUserRepository.UpdateAsync(user);
            return Result<bool>.Success(true);
        }

        public async Task<Result<AppUserDto>> UpdateProfileAsync(int id, UpdateProfileRequest request)
        {
            var user = await _appUserRepository.GetByIdAsync(id);
            if (user == null || user.IsDeleted)
                return Result<AppUserDto>.Failure(ErrorCode.NotFound, "Không tìm thấy người dùng");

            user.FirstName = request.FirstName;
            user.LastName = request.LastName;
            user.Phone = request.Phone;
            user.Bio = request.Bio;
            user.Address = request.Address;
            user.LastModifiedAt = DateTime.UtcNow;
            user.LastModifiedById = _currentUserService.GetUserId();

            await _appUserRepository.UpdateAsync(user);
            return Result<AppUserDto>.Success(user.Adapt<AppUserDto>());
        }

        public async Task<Result<bool>> ChangePasswordAsync(int id, ChangePasswordRequest request)
        {
            var user = await _appUserRepository.GetByIdAsync(id);
            if (user == null || user.IsDeleted)
                return Result<bool>.Failure(ErrorCode.NotFound, "Không tìm thấy người dùng");

            // Verify current password
            if (!BCrypt.Net.BCrypt.Verify(request.CurrentPassword, user.PasswordHash))
                return Result<bool>.Failure(ErrorCode.InvalidCredentials, "Mật khẩu hiện tại không chính xác");

            // Update password
            user.PasswordHash = PasswordHelper.HassPassword(request.NewPassword);
            user.LastModifiedAt = DateTime.UtcNow;
            user.LastModifiedById = _currentUserService.GetUserId();

            await _appUserRepository.UpdateAsync(user);
            return Result<bool>.Success(true);
        }

        public async Task<Result<AppUserDto>> GetCurrentUserAsync()
        {
            var currentUserId = _currentUserService.GetUserId();
            if (currentUserId == null)
                return Result<AppUserDto>.Failure(ErrorCode.Unauthorized, "Người dùng chưa đăng nhập");

            return await GetByIdAsync(currentUserId);
        }

        public async Task<Result<bool>> SetHasCompletedInitialSetupAsync(int userId)
        {
            var user = await _appUserRepository.GetByIdAsync(userId);
            if (user == null || user.IsDeleted)
                return Result<bool>.Failure(ErrorCode.NotFound, "Không tìm thấy người dùng");

            if (user.HasCompletedInitialSetup)
                return Result<bool>.Success(true);

            user.HasCompletedInitialSetup = true;
            user.LastModifiedAt = DateTime.UtcNow;
            user.LastModifiedById = _currentUserService.GetUserId();

            await _appUserRepository.UpdateAsync(user);
            return Result<bool>.Success(true);
        }
	}
}

