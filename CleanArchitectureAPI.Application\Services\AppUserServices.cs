using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.Shared.Helpers;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Interfaces.User;

namespace CleanArchitectureAPI.Application.Services
{
    public class AppUserServices : IAppUserServices
    {
        private readonly IAppUserRepository _appUserRepository;
        public AppUserServices(IAppUserRepository appUserRepository) {
            _appUserRepository = appUserRepository;
        }

        public async Task<AppUser> CreateAppUserAsync(CreateAppUserDto createAppUserDto)
        {
            var appUser = new AppUser();
            appUser.Email = createAppUserDto.EmailAddress;
            appUser.PasswordHash = PasswordHelper.HassPassword(createAppUserDto.Password);
            return appUser;
        }
        
        public Task<bool> DeleteAppUserAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task<IEnumerable<AppUser>> GetAllAppUsersAsync()
        {
            throw new NotImplementedException();
        }

        public Task<AppUser> GetAppUserByIdAsync(int id)
        {
            throw new NotImplementedException();
        }
    }
}
