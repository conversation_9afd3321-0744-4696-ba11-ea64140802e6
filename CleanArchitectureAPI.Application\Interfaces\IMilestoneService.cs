﻿using CleanArchitecture.API.Requests.Milestone;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Requests.MilestoneRequests;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IMilestoneService
    {
        Task<Result<IEnumerable<MilestoneDto>>> GetAllByProjectKeyAsync(string projectKey);
        Task<Result<MilestoneDto>> GetByIdAsync(int id);
        Task<Result<MilestoneDto>> CreateAsync(CreateMilestoneRequest request);
        Task<Result<bool>> UpdateAsync(int id, UpdateMilestoneRequest request);
        Task<Result<bool>> SoftDeleteAsync(int id);
        Task<Result<bool>> ReorderAsync(ReorderMilestoneRequest request);
    }
}