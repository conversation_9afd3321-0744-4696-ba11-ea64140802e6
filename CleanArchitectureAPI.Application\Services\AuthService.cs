using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using CleanArchitecture.API.DTOs.AppUser;
using CleanArchitecture.Shared.Helpers;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.Requests.Auth;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Shared.Helpers;
using Microsoft.IdentityModel.Tokens;

namespace CleanArchitectureAPI.Application.Services
{
    public class AuthService : IAuthService
    {
        private readonly IAuthRepository _authRepository;

        public AuthService(IAuthRepository authRepository)
        {
            _authRepository = authRepository;
        }


		public async Task<Result<AuthResultDto>> LoginAsync(string email, string password)
		{
			var appUser = await _authRepository.GetUserByEmailAsync(email);
            if (appUser == null)
                return Result<AuthResultDto>.Failure(ErrorCode.NotFound, "Email không tồn tại trong hệ thống");

			var isPasswordValid = BCrypt.Net.BCrypt.Verify(password, appUser.PasswordHash);
			if (!isPasswordValid)
				return Result<AuthResultDto>.Failure(ErrorCode.InvalidCredentials, "Mật khẩu không chính xác");

			var token = JwtHelper.GenerateToken(appUser, "your-very-secret-key-here-123456");

            return Result<AuthResultDto>.Success(new AuthResultDto {Email = appUser.Email, AccessToken = token, Id = appUser.Id , HasCompletedInitialSetup = appUser.HasCompletedInitialSetup});
		}


		public async Task<Result<AuthResultDto>> RegisterAsync(RegisterUserRequest registerUserDto)
        {
          
            if (await _authRepository.IsEmailExistsAsync(registerUserDto.Email))
				return Result<AuthResultDto>.Failure(ErrorCode.Conflict, "Email đã tồn tại");
			var passwordHash = PasswordHelper.HassPassword(registerUserDto.Password);

            AppUser appUser = new AppUser
            {
                Email = registerUserDto.Email,
                PasswordHash = passwordHash,
            };

            await _authRepository.CreateAsync(appUser);

            var token = JwtHelper.GenerateToken(appUser , "your-very-secret-key-here-123456");
            return Result<AuthResultDto>.Success(new AuthResultDto { Email = registerUserDto.Email, AccessToken = token, Id = appUser.Id });
        }

        public async Task<Result<bool>> ChangePassword(int id, string oldPassword, string newPassword)
        {
            var user = await _authRepository.GetByIdAsync(id);
            if (user == null)
               return Result<bool>.Success(false);

            // Verify old password
            if (!BCrypt.Net.BCrypt.Verify(oldPassword, user.PasswordHash))
                return Result<bool>.Success(false);

            // Update password
            user.PasswordHash = PasswordHelper.HassPassword(newPassword);
            user.LastModifiedAt = DateTime.UtcNow;

            await _authRepository.UpdateAsync(user);
            return Result<bool>.Success(true);
            ;
        }
    }
}

