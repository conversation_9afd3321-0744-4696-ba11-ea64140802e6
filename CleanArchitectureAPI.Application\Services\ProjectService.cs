
using CleanArchitecture.API.Requests.Project;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;

namespace CleanArchitectureAPI.Application.Services
{
	public class ProjectService : IProjectService
	{
		private readonly IProjectRepository _projectRepository;
		private readonly IIssueTypeRepository _issueTypeRepository;
		private readonly IStatusRepository _statusRepository;
		private readonly IPriorityRepository _priorityRepository;
		private readonly ICurrentUserService _currentUserService;
		private readonly IAppUserServices _appUserServices;

		public ProjectService(
			IProjectRepository projectRepository,
			IIssueTypeRepository issueTypeRepository,
			IStatusRepository statusRepository,
			IPriorityRepository priorityRepository,
			ICurrentUserService currentUserService,
			IAppUserServices appUserServices)
		{
			_projectRepository = projectRepository;
			_issueTypeRepository = issueTypeRepository;
			_statusRepository = statusRepository;
			_priorityRepository = priorityRepository;
			_currentUserService = currentUserService;
			_appUserServices = appUserServices;
		}

        public async Task<Result<bool>> CheckProjectKeyDuplicate(string projectKey)
        {
            bool project = await _projectRepository.IsProjectKeyDuplicatedAsync(projectKey);
            if (project)
                return Result<bool>.Success(true);
            return Result<bool>.Success(false);
        }

        public async Task<Result<Project>> CreateAsync(CreateProjectRequest request)
		{
			//Check project exists
			bool projectExists = await _projectRepository.IsProjectKeyDuplicatedAsync(request.ProjectKey);
			if (projectExists) return Result<Project>.Failure(ErrorCode.Conflict, "Mã dự án đã tồn tại");

			//basic-info  
			Project project = new Project();
			project.Name = request.Name;
			project.ProjectKey = request.ProjectKey;
			project.IsActive = true;

			//audit  
			project.CreatedAt = DateTime.UtcNow;
			project.CreatedById = _currentUserService.GetUserId();

			await _projectRepository.CreateAsync(project);

			if (!_currentUserService.HasCompletedInitialSetup())
			{
				await _appUserServices.SetHasCompletedInitialSetupAsync(_currentUserService.GetUserId());
			}



			// Tạo các entity mặc định: IssueType, Status, Priority
			// IssueTypes mặc định
			var defaultIssueTypes = new List<IssueType>
			{
				new IssueType { Name = "Task", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Color = "#4B9EFF" },
				new IssueType { Name = "Bug", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Color = "#FF4B4B" },
				new IssueType { Name = "Story", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Color = "#36B37E" }
			};
			foreach (var issueType in defaultIssueTypes)
			{
				await _issueTypeRepository.CreateAsync(issueType);
			}

			// Statuses mặc định
			var defaultStatuses = new List<Status>
			{
				new Status { Name = "To Do", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Order = 1, Color = "#BDBDBD" },
				new Status { Name = "In Progress", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Order = 2, Color = "#1976D2" },
				new Status { Name = "Done", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Order = 3, Color = "#388E3C" }
			};
			foreach (var status in defaultStatuses)
			{
				await _statusRepository.CreateAsync(status);
			}

			// Priorities mặc định
			var defaultPriorities = new List<Priority>
			{
				new Priority { Name = "Low", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Order = 1, Color = "#8BC34A" },
				new Priority { Name = "Medium", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Order = 2, Color = "#FFC107" },
				new Priority { Name = "High", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Order = 3, Color = "#F44336" }
			};
			foreach (var priority in defaultPriorities)
			{
				await _priorityRepository.CreateAsync(priority);
			}

			return Result<Project>.Success(project);
		}

		public Task<IEnumerable<Result<Project>>> GetAllByProjectUserIdAsync(int id)
		{
			throw new NotImplementedException();
		}

		public async Task<Result<Project>> GetByIdAsync(int id)
		{
			var project = await _projectRepository.GetByIdAsync(id);
			if (project == null)
				return Result<Project>.Failure(ErrorCode.NotFound, "Không tìm thấy dự án nào");

			return Result<Project>.Success(project);
		}

		public Task<Result<bool>> SoftDeleteAsync(int id)
		{
			throw new NotImplementedException();
		}

		public Task<Result<bool>> UpdateAsync(int id, UpdateProjectRequest dto)
		{
			throw new NotImplementedException();
		}
	}
}
