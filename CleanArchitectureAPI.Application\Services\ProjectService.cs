
using CleanArchitecture.API.Requests.Project;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;

namespace CleanArchitectureAPI.Application.Services
{
	public class ProjectService : IProjectService
	{
		private readonly IProjectRepository _projectRepository;
		private readonly ICurrentUserService _currentUserService;

		public ProjectService(IProjectRepository projectRepository, ICurrentUserService currentUserService)
		{
			_projectRepository = projectRepository;
			_currentUserService = currentUserService;
		}

        public async Task<Result<bool>> CheckProjectKeyDuplicate(string projectKey)
        {
          bool project = await _projectRepository.IsProjectKeyDuplicatedAsync(projectKey);
			if (project)
				return Result<bool>.Success(true);
			return Result<bool>.Success(false);
        }

        public async Task<Result<Project>> CreateAsync(CreateProjectRequest request)
		{
			//Check project exists
			bool projectExists = await _projectRepository.IsProjectKeyDuplicatedAsync(request.ProjectKey);
			if (projectExists) return Result<Project>.Failure(ErrorCode.Conflict, "Mã dự án đã tồn tại");


			//basic-info  
			Project project = new Project();
			project.Name = request.Name;
			project.ProjectKey = request.ProjectKey;
			project.IsActive = true;

			//audit  
			project.CreatedAt = DateTime.UtcNow;
			project.CreatedById = _currentUserService.GetUserId();

			await _projectRepository.CreateAsync(project);

			return Result<Project>.Success(project);
		}

		public Task<IEnumerable<Result<Project>>> GetAllByProjectUserIdAsync(int id)
		{
			throw new NotImplementedException();
		}

	

		public async Task<Result<Project>> GetByIdAsync(int id)
		{
			var project =await _projectRepository.GetByIdAsync(id);
			if (project == null)
				return Result<Project>.Failure(ErrorCode.NotFound, "Không tìm thấy dự án nào");
			
			return Result<Project>.Success(project);
		}

		public Task<Result<bool>> SoftDeleteAsync(int id)
		{
			throw new NotImplementedException();
		}

		public Task<Result<bool>> UpdateAsync(int id, UpdateProjectRequest dto)
		{
			throw new NotImplementedException();
		}
	}
}
