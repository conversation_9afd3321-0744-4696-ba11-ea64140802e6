using FluentValidation;
using CleanArchitectureAPI.Application.Requests.AppUser;

namespace CleanArchitectureAPI.Application.Validators.AppUser
{
    public class UpdateProfileRequestValidator : AbstractValidator<UpdateProfileRequest>
    {
        public UpdateProfileRequestValidator()
        {
            RuleFor(x => x.FirstName)
                .NotEmpty().WithMessage("Vui lòng nhập họ.")
                .MaximumLength(100).WithMessage("Họ không được vượt quá 100 ký tự.");

            RuleFor(x => x.LastName)
                .NotEmpty().WithMessage("Vui lòng nhập tên.")
                .MaximumLength(100).WithMessage("Tên không được vượt quá 100 ký tự.");

            RuleFor(x => x.Phone)
                .MaximumLength(20).WithMessage("Số điện thoại không được vượt quá 20 ký tự.")
                .When(x => !string.IsNullOrEmpty(x.Phone));

            RuleFor(x => x.Bio)
                .MaximumLength(500).WithMessage("Tiểu sử không được vượt quá 500 ký tự.")
                .When(x => !string.IsNullOrEmpty(x.Bio));

            RuleFor(x => x.Address)
                .MaximumLength(255).WithMessage("Địa chỉ không được vượt quá 255 ký tự.")
                .When(x => !string.IsNullOrEmpty(x.Address));
        }
    }
}
