using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
	public class ResolutionRepository : Repository<Resolution>, IResolutionRepository
	{
		public ResolutionRepository(ApplicationDbContext context) : base(context)
		{

		}

		public async Task<bool> IsNameDuplicated(string projectKey, string name)
		{
			return await _context.Resolutions.AnyAsync(p => p.Name == name && p.ProjectKey == projectKey && !p.IsDeleted);
		}

		public async Task<int> GetMaxOrderInProjectAsync(string projectKey)
		{
			return await _context.Resolutions
				.Where(c => c.ProjectKey == projectKey && !c.IsDeleted)
				.Select(c => (int?)c.Order)
				.MaxAsync() ?? 0;
		}

		public async Task<IEnumerable<Resolution>> GetAllByProjectKeyAsync(string projectKey)
		{
			var resolutions = await _context.Resolutions.Where(c => c.ProjectKey == projectKey && !c.IsDeleted).OrderBy(c => c.Order).ToListAsync();
			return resolutions;
		}

		public async Task ReorderResolutionsAsync(string projectKey, List<int> resolutionIdsInOrder)
		{
			var resolutions = await _context.Resolutions
				.Where(s => s.ProjectKey == projectKey && !s.IsDeleted && resolutionIdsInOrder.Contains(s.Id))
				.ToListAsync();

			for (int i = 0; i < resolutionIdsInOrder.Count; i++)
			{
				var resolution = resolutions.FirstOrDefault(s => s.Id == resolutionIdsInOrder[i]);
				if (resolution != null)
				{
					resolution.Order = i + 1;
					_context.Resolutions.Update(resolution);
				}
			}

			await _context.SaveChangesAsync();
		}
	}
}
