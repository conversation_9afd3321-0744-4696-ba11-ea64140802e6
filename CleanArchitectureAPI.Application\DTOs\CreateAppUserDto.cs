﻿using CleanArchitectureAPI.Domain.Enums;

namespace CleanArchitectureAPI.Application.DTOs
{
    public class CreateAppUserDto
    {
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public required string Email { get; set; }
        public required string Password { get; set; }
        public string? Phone { get; set; }
        public string? Bio { get; set; }
        public string? Address { get; set; }
        public UserStatus Status { get; set; } = UserStatus.Online;
    }

    public class UpdateAppUserDto
    {
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Bio { get; set; }
        public string? Address { get; set; }
        public UserStatus Status { get; set; }
    }

    public class AppUserDto
    {
        public int Id { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string FullName => $"{FirstName} {LastName}".Trim();
        public string Email { get; set; } = string.Empty;
        public UserStatus Status { get; set; }
        public string? Avatar { get; set; }
        public string? Phone { get; set; }
        public string? Bio { get; set; }
        public string? Address { get; set; }
        public DateTime LastActive { get; set; }
        public DateTime LastLoginTime { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }
        public bool IsActive { get; set; }
        public bool HasCompletedInitialSetup { get; set; }

	}

    public class ChangePasswordDto
    {
        public required string CurrentPassword { get; set; }
        public required string NewPassword { get; set; }
        public required string ConfirmPassword { get; set; }
    }

    public class UpdateProfileDto
    {
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Bio { get; set; }
        public string? Address { get; set; }
    }
}
